import 'package:flutter/material.dart';

class StaticSplashScreen extends StatefulWidget {
  final VoidCallback onSplashComplete;

  const StaticSplashScreen({
    super.key,
    required this.onSplashComplete,
  });

  @override
  State<StaticSplashScreen> createState() => _StaticSplashScreenState();
}

class _StaticSplashScreenState extends State<StaticSplashScreen>
    with TickerProviderStateMixin {
  bool _hasSplashCompleted = false;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startSplashSequence();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
  }

  void _startSplashSequence() async {
    // Start fade animation
    _fadeController.forward();

    // Show splash for 3 seconds
    await Future.delayed(const Duration(seconds: 3));
    _onSplashComplete();
  }

  void _onSplashComplete() {
    if (_hasSplashCompleted) return;

    _hasSplashCompleted = true;
    widget.onSplashComplete();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[900], // Grey background as preferred
      body: Stack(
        children: [
          // Animated GameFlex logo splash
          Center(
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // GameFlex logo
                  Image.asset(
                    'assets/images/logos/gameflexBranding.png',
                    width: 200,
                    height: 200,
                    fit: BoxFit.contain,
                  ),
                  const SizedBox(height: 32),
                  // Loading indicator
                  const CircularProgressIndicator(
                    color: Colors.white,
                  ),
                ],
              ),
            ),
          ),

          // Tap to skip (optional - you can remove this if you don't want skip functionality)
          Positioned.fill(
            child: GestureDetector(
              onTap: () {
                if (!_hasSplashCompleted) {
                  _onSplashComplete();
                }
              },
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),

          // Skip button (optional)
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            right: 16,
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: TextButton(
                onPressed: () {
                  if (!_hasSplashCompleted) {
                    _onSplashComplete();
                  }
                },
                child: const Text(
                  'Skip',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
